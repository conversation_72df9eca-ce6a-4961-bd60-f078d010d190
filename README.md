<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON>gatu <EMAIL>
 * @Date: 2025-04-21 10:00:34
 * @LastEditors: <NAME_EMAIL>
 * @LastEditTime: 2025-04-24 09:41:42
 * @FilePath: \Wiz-Aroma-Adama\README.md
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->

# WizAroma Delivery Bot

🍽️ A Telegram-based food delivery system for WizAroma. Features user ordering, admin management, payment verification, and system maintenance capabilities.

## Features

- **Multi-Restaurant Selection**: Browse restaurants by area
- **Smart Menu System**: Easy-to-use menu interface with categories and prices
- **Points System**: Earn 11% (displayed as 10+1%) of delivery fee as points
- **Multiple Payment Methods**:
  - Telebirr
  - CBE Bank Transfer
  - BOA Bank Transfer
  - Points redemption for delivery fees
- **Favorite Orders**: Save and reorder your favorite meals with one click
- **Multi-Bot Architecture**: Separate bots for users, admins, finance, and maintenance

## Operating Hours

- **Weekdays**: 5:30-7:30 and 11:30-14:30 (Ethiopian Time)
- **Weekends**: 5:30-14:30 (Ethiopian Time)

## User Guide

### Customer Bot

1. Start with `/start`
2. Main Menu Options:
   - 🍽️ Order Food
   - 💫 My Points
   - ⭐ My Favorites
   - ℹ️ Help
3. Ordering Process:
   - Select area
   - Choose restaurant
   - Add menu items
   - Add special instructions (optional)
   - Select delivery location
   - Provide delivery name and phone number
   - Review and confirm order
   - Choose payment method
   - Send payment proof
   - Wait for verification

## Documentation

Detailed documentation is available in the following files:

- **menu_and_restaurants.txt** - Information about available restaurants and menus
- **delivery_fees.txt** - Information about delivery locations and fees
- **DATA_SYNC.md** - Information about the data synchronization system between Railway and GitHub
- **SECURITY.md** - Security best practices for handling credentials and API keys
- **FIREBASE_SETUP.md** - Instructions for setting up Firebase integration
- **DEPLOYMENT.md** - Comprehensive deployment guide for various environments

## Support

For issues or questions:

- Email: <<EMAIL>>
- Telegram: @wiz_aroma_bot

## Author

Mihretab Nigatu

## Bot Structure

The system uses multiple bot instances for different functions:

1. **User Bot**: Handles user interactions, order placement, and general user communication
2. **Admin Bot**: Provides administrative functions for managing restaurants, menus, etc.
3. **Finance Bot**: Processes payment receipts and approves/rejects orders
4. **Maintenance Bot**: Provides maintenance and management functions
5. **Notification Bot**: Sends formatted order details to a specified channel when orders are approved
