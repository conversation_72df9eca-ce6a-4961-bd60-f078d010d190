"""
Favorite orders handlers for the Wiz Aroma Delivery Bot.
Contains handlers for viewing, creating, and deleting favorite orders.
"""

from telebot import types

from src.bot_instance import bot
from src.config import logger
from src.utils.keyboards import (
    get_main_menu_markup,
    get_favorite_orders_markup,
    get_delete_favorite_markup,
)
from src.data_models import (
    orders,
    order_status,
    user_names,
    user_phone_numbers,
    current_order_numbers,
    delivery_locations,
)
from src.data_storage import (
    get_favorite_orders,
    save_favorite_order,
    delete_favorite_order,
    clean_up_order_data,
    save_user_data,
)
from src.utils.time_utils import generate_order_number


from src.utils.text_utils import escape_markdown


@bot.message_handler(func=lambda message: message.text == "⭐ Favorite Orders")
def show_favorite_orders(message):
    """Show all favorite orders for the user"""
    try:
        user_id = message.from_user.id

        # Get user's favorite orders
        favorites = get_favorite_orders(user_id)
        num_favorites = len(favorites)

        if num_favorites == 0:
            bot.send_message(
                message.chat.id,
                "You don't have any favorite orders yet.\n"
                "To create a favorite order, start an order and choose '💾 Save as Favorite' at the end.",
                reply_markup=get_favorite_orders_markup(num_favorites),
            )
        else:
            # Create message with list of favorite orders
            order_list = "Your favorite orders:\n\n"
            for i, favorite in enumerate(favorites):
                order_name = favorite.get("favorite_name", f"Order #{i+1}")
                restaurant = favorite.get("restaurant", "Unknown Restaurant")
                items_count = len(favorite.get("items", []))
                total = sum(item.get("price", 0) for item in favorite.get("items", []))

                order_list += f"🔄 {order_name}\n"
                order_list += f"  • Restaurant: {restaurant}\n"
                order_list += f"  • Items: {items_count}\n"
                order_list += f"  • Total: {total} birr\n\n"

            # Add instructions
            order_list += "Select an order to view details or place the order."

            bot.send_message(
                message.chat.id,
                order_list,
                reply_markup=get_favorite_orders_markup(num_favorites, favorites),
            )

        # Set the user status
        order_status[user_id] = "VIEWING_FAVORITE_ORDERS"

    except Exception as e:
        logger.error(f"Error in show_favorite_orders: {e}")
        bot.send_message(
            message.chat.id,
            "Sorry, there was an error showing your favorite orders. Please try again later.",
            reply_markup=get_main_menu_markup(),
        )


@bot.message_handler(
    func=lambda message: (
        message.text
        and message.text.startswith("🔄 ")
        and order_status.get(message.from_user.id) == "VIEWING_FAVORITE_ORDERS"
    )
)
def use_favorite_order(message):
    """Handle using a favorite order"""
    try:
        user_id = message.from_user.id
        favorites = get_favorite_orders(user_id)

        # Extract the order name from the message
        selected_order_text = message.text[2:].strip()  # Remove the 🔄 icon

        # Find the matching favorite order
        found_order = False
        order_index = -1

        # First try direct match by name
        for i, favorite in enumerate(favorites):
            order_name = favorite.get("favorite_name", f"Order #{i+1}")
            if order_name == selected_order_text:
                order_index = i
                found_order = True
                break

        # If not found, try find it by Order #N format as a fallback
        if not found_order and selected_order_text.startswith("Order #"):
            try:
                order_num = int(selected_order_text.split("#")[1]) - 1
                if 0 <= order_num < len(favorites):
                    order_index = order_num
                    found_order = True
            except (ValueError, IndexError):
                pass

        if found_order and 0 <= order_index < len(favorites):
            favorite_order = favorites[order_index]

            # Initialize or reset user's order with the favorite order data
            orders[user_id] = favorite_order.copy()

            # Ensure we have the necessary values for processing
            if "delivery_gate" in favorite_order:
                delivery_locations[user_id] = favorite_order["delivery_gate"]

            # Generate order confirmation message
            order_details = "\n".join(
                [
                    f"• {escape_markdown(item['name'])}: {item['price']} birr"
                    for item in favorite_order.get("items", [])
                ]
            )
            total_price = sum(item["price"] for item in favorite_order.get("items", []))
            delivery_fee = favorite_order.get("delivery_fee", 0)

            restaurant_name = favorite_order.get("restaurant", "Unknown Restaurant")
            delivery_gate = favorite_order.get("delivery_gate", "Unknown Location")
            order_description = favorite_order.get("order_description", "None")

            # Add the saved name and phone number - use user_name from favorite order if available
            delivery_name = favorite_order.get(
                "user_name", favorite_order.get("delivery_name", "")
            )
            phone_number = favorite_order.get("phone_number", "")

            # Save these user values for persistence
            user_names[user_id] = delivery_name
            user_phone_numbers[user_id] = phone_number
            save_user_data()

            # Include delivery fee in total
            total_with_delivery = total_price + delivery_fee

            # Generate a new order number
            order_number = generate_order_number(user_id)
            current_order_numbers[user_id] = order_number
            orders[user_id]["order_number"] = order_number

            # Get Telegram username if available
            telegram_username = message.from_user.username or "Not provided"
            # Store Telegram username in the order
            orders[user_id]["telegram_username"] = telegram_username

            # Escape Markdown in user-provided content
            safe_restaurant_name = escape_markdown(restaurant_name)
            safe_delivery_gate = escape_markdown(delivery_gate)
            safe_order_description = escape_markdown(order_description)
            safe_delivery_name = escape_markdown(delivery_name)
            safe_phone_number = escape_markdown(phone_number)
            # Telegram username not used in summary but stored in order

            # Create order summary
            summary = (
                f"🧾 ORDER SUMMARY - #{order_number}\n\n"
                f"👤 Name: {safe_delivery_name}\n"
                f"📱 Phone: {safe_phone_number}\n"
                f"📍 Delivery to: {safe_delivery_gate}\n\n"
                f"🏪 Restaurant: {safe_restaurant_name}\n\n"
                f"📋 Items:\n{order_details}\n"
                f"📝 Special Instructions: {safe_order_description}\n\n"
                f"💰 Subtotal: {total_price} birr\n"
                f"🚚 Delivery Fee: {delivery_fee} birr\n"
                f"💵 Total: {total_with_delivery} birr\n\n"
                "Please confirm your order to proceed."
            )

            # Create confirmation buttons
            markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
            markup.add(
                types.KeyboardButton("✅ Confirm Order"),
                types.KeyboardButton("❌ Cancel Order"),
                types.KeyboardButton("🔙 Back to Favorite Orders"),
            )

            # Send the order summary to the user
            bot.send_message(
                message.chat.id, summary, reply_markup=markup, parse_mode="Markdown"
            )

            # Update order status
            order_status[user_id] = "AWAITING_CONFIRMATION"
        else:
            bot.send_message(
                message.chat.id,
                "Invalid order selection. Please select a valid favorite order.",
                reply_markup=get_favorite_orders_markup(len(favorites), favorites),
            )

    except Exception as e:
        logger.error(f"Error in use_favorite_order: {e}")
        bot.send_message(
            message.chat.id,
            "Sorry, there was an error using your favorite order. Please try again.",
            reply_markup=get_main_menu_markup(),
        )


@bot.message_handler(func=lambda message: message.text == "🗑️ Delete Favorite Order")
def delete_favorite_order_handler(message):
    """Show menu to delete a favorite order"""
    try:
        user_id = message.from_user.id
        favorites = get_favorite_orders(user_id)

        if not favorites:
            bot.send_message(
                message.chat.id,
                "You don't have any favorite orders to delete.",
                reply_markup=get_favorite_orders_markup(0),
            )
            return

        bot.send_message(
            message.chat.id,
            "Select a favorite order to delete:",
            reply_markup=get_delete_favorite_markup(len(favorites), favorites),
        )

        order_status[user_id] = "DELETING_FAVORITE_ORDER"

    except Exception as e:
        logger.error(f"Error in delete_favorite_menu: {e}")
        bot.send_message(
            message.chat.id,
            "Sorry, there was an error showing the delete menu. Please try again.",
            reply_markup=get_favorite_orders_markup(
                len(get_favorite_orders(message.from_user.id)),
                get_favorite_orders(message.from_user.id),
            ),
        )


@bot.message_handler(
    func=lambda message: (
        message.text
        and (
            message.text.startswith("🗑️ Delete Order #")
            or message.text.startswith("🗑️ Delete: ")
        )
        and order_status.get(message.from_user.id) == "DELETING_FAVORITE_ORDER"
    )
)
def confirm_delete_favorite_order(message):
    """Handle deletion of a favorite order"""
    try:
        user_id = message.from_user.id
        favorites = get_favorite_orders(user_id)

        # Find the order to delete
        order_index = -1

        if message.text.startswith("🗑️ Delete Order #"):
            # Legacy format - extract order number
            try:
                order_index = int(message.text.split("#")[1]) - 1
            except (ValueError, IndexError):
                order_index = -1
        elif message.text.startswith("🗑️ Delete: "):
            # New format - extract order name
            order_name = message.text[len("🗑️ Delete: ") :]

            # Find matching order by name
            for i, favorite in enumerate(favorites):
                if favorite.get("favorite_name") == order_name:
                    order_index = i
                    break

        # Delete the favorite order if found
        if 0 <= order_index < len(favorites):
            success = delete_favorite_order(user_id, order_index)

            # Get updated favorites list
            updated_favorites = get_favorite_orders(user_id)

            if success:
                bot.send_message(
                    message.chat.id,
                    "✅ Favorite order deleted successfully.",
                    reply_markup=get_favorite_orders_markup(
                        len(updated_favorites), updated_favorites
                    ),
                )
            else:
                bot.send_message(
                    message.chat.id,
                    "❌ Error deleting favorite order. Please try again.",
                    reply_markup=get_favorite_orders_markup(len(favorites), favorites),
                )
        else:
            bot.send_message(
                message.chat.id,
                "❌ Could not find the order to delete. Please try again.",
                reply_markup=get_favorite_orders_markup(len(favorites), favorites),
            )

        order_status[user_id] = "VIEWING_FAVORITE_ORDERS"

    except Exception as e:
        logger.error(f"Error in confirm_delete_favorite_order: {e}")
        bot.send_message(
            message.chat.id,
            "Sorry, there was an error deleting your favorite order. Please try again.",
            reply_markup=get_favorite_orders_markup(
                len(get_favorite_orders(message.from_user.id)),
                get_favorite_orders(message.from_user.id),
            ),
        )


@bot.message_handler(func=lambda message: message.text == "🔙 Back to Favorite Orders")
def back_to_favorite_orders(message):
    """Return to the favorite orders menu"""
    try:
        user_id = message.from_user.id

        # Clean up current order if any
        if user_id in orders:
            clean_up_order_data(user_id, None)

        favorites = get_favorite_orders(user_id)

        # Create message with list of favorite orders
        if len(favorites) > 0:
            order_list = "Your favorite orders:\n\n"
            for i, favorite in enumerate(favorites):
                order_name = favorite.get("favorite_name", f"Order #{i+1}")
                restaurant = favorite.get("restaurant", "Unknown Restaurant")
                items_count = len(favorite.get("items", []))
                total = sum(item.get("price", 0) for item in favorite.get("items", []))

                order_list += f"🔄 {order_name}\n"
                order_list += f"  • Restaurant: {restaurant}\n"
                order_list += f"  • Items: {items_count}\n"
                order_list += f"  • Total: {total} birr\n\n"

            # Add instructions
            order_list += "Select an order to view details or place the order."

            bot.send_message(
                message.chat.id,
                order_list,
                reply_markup=get_favorite_orders_markup(len(favorites), favorites),
            )
        else:
            bot.send_message(
                message.chat.id,
                "You don't have any favorite orders yet.",
                reply_markup=get_favorite_orders_markup(0),
            )

        order_status[user_id] = "VIEWING_FAVORITE_ORDERS"

    except Exception as e:
        logger.error(f"Error in back_to_favorite_orders: {e}")
        bot.send_message(
            message.chat.id,
            "Sorry, there was an error. Returning to main menu.",
            reply_markup=get_main_menu_markup(),
        )


@bot.message_handler(
    func=lambda message: message.text == "➕ Create New Favorite Order"
)
def create_new_favorite(message):
    """Start process to create a new favorite order"""
    try:
        # Redirect to the main order flow
        from src.handlers.main_handlers import show_areas

        # Set status to indicate we're creating a favorite order
        user_id = message.from_user.id
        order_status[user_id] = "CREATING_FAVORITE_ORDER"

        # Redirect to area selection
        show_areas(message)

    except Exception as e:
        logger.error(f"Error in create_new_favorite: {e}")
        bot.send_message(
            message.chat.id,
            "Sorry, there was an error starting your favorite order. Please try again.",
            reply_markup=get_main_menu_markup(),
        )


def prompt_for_favorite_name(message):
    """Ask the user to name their favorite order"""
    try:
        user_id = message.from_user.id

        # Create keyboard with cancel option
        markup = types.ReplyKeyboardMarkup(row_width=1, resize_keyboard=True)
        markup.add(types.KeyboardButton("❌ Cancel"))

        bot.send_message(
            message.chat.id,
            "Please enter a name for your favorite order (max 30 characters):",
            reply_markup=markup,
        )

        # Update status to indicate we're now waiting for a name
        order_status[user_id] = "AWAITING_FAVORITE_NAME"

    except Exception as e:
        logger.error(f"Error in prompt_for_favorite_name: {e}")
        bot.send_message(
            message.chat.id,
            "Sorry, there was an error. Please try again.",
            reply_markup=get_main_menu_markup(),
        )


@bot.message_handler(
    func=lambda message: order_status.get(message.from_user.id)
    == "AWAITING_FAVORITE_NAME"
)
def save_favorite_order_handler(message):
    """Save the current order as a favorite with the provided name"""
    try:
        user_id = message.from_user.id

        # Cancel if requested
        if message.text == "❌ Cancel":
            bot.send_message(
                message.chat.id,
                "Canceled saving as favorite. Returning to main menu.",
                reply_markup=get_main_menu_markup(),
            )
            # Reset order status
            order_status[user_id] = None
            return

        order_name = message.text.strip()

        # Validate name length
        if not order_name or len(order_name) > 30:
            bot.send_message(
                message.chat.id,
                "Please enter a valid name (maximum 30 characters):",
            )
            return

        # Get the current order
        if user_id not in orders:
            bot.send_message(
                message.chat.id,
                "No active order found to save as favorite.",
                reply_markup=get_main_menu_markup(),
            )
            return

        current_order = orders[user_id]

        # Add Telegram username to the order
        telegram_username = message.from_user.username or "Not provided"
        current_order["telegram_username"] = telegram_username

        # Save the order as a favorite
        success = save_favorite_order(user_id, order_name, current_order)

        if success:
            bot.send_message(
                message.chat.id,
                f"✅ Your order has been saved as '{order_name}'!",
                reply_markup=get_main_menu_markup(),
            )

            # Clean up the order data to start fresh
            clean_up_order_data(user_id, current_order.get("order_number"))
        else:
            bot.send_message(
                message.chat.id,
                "❌ There was an error saving your favorite order. Please try again.",
                reply_markup=get_main_menu_markup(),
            )

    except Exception as e:
        logger.error(f"Error in save_favorite_order_handler: {e}")
        bot.send_message(
            message.chat.id,
            "Sorry, there was an error saving your favorite order. Please try again.",
            reply_markup=get_main_menu_markup(),
        )


@bot.message_handler(commands=["favorites"])
def favorites_command(message):
    """Handle the /favorites command"""
    # Reuse the existing show_favorite_orders function
    message.text = "⭐ Favorite Orders"
    show_favorite_orders(message)


def register_handlers():
    """Register all handlers in this module"""
    # Explicitly list all handlers for clarity
    handlers = [
        show_favorite_orders,
        use_favorite_order,
        delete_favorite_order_handler,
        confirm_delete_favorite_order,
        back_to_favorite_orders,
        create_new_favorite,
        save_favorite_order_handler,
        favorites_command,
    ]

    # Just log that handlers are registered - since they're already decorated
    logger.info(f"Registered {len(handlers)} favorite orders handlers")
